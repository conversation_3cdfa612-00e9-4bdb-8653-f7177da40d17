import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/condition_profit_stop_loss_order/edit_take_profit_stop_loss_order_widget.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void opendEditConditionProfitStopLossOrderBottomSheet({
  required BuildContext context,
  required ConditionOrderBookModel item,
  required VoidCallback onEditSuccess,
}) {
  VPPopup.bottomSheet(
    MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => EditConditionOrderCubit()),
        BlocProvider(create: (context) => ValidateConditionOrderCubit()),
        BlocProvider(create: (context) => ValidateOrderCubit()),
        BlocProvider(
          create:
              (context) => PlaceOrderCubit(
                symbol: item.symbol ?? '',
                action:
                    item.orderTypeEnum == OrderTypeEnum.buy
                        ? OrderAction.buy
                        : OrderAction.sell,
                accountModel:
                    GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .isNotEmpty
                        ? GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .first
                        : null,
                subAccountType:
                    GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .isNotEmpty
                        ? GetIt.instance<SubAccountCubit>()
                            .subAccountsAllHaveDerivative
                            .where((e) => e.id == item.accountId)
                            .first
                            .toSubAccountType
                        : SubAccountType.normal,
              ),
        ),
        BlocProvider(create: (context) => StockInfoCubit()),
        BlocProvider(
          create:
              (context) =>
                  AvailableTradeCubit()..getAvailableTrade(
                    accountId: item.accountId ?? "",
                    symbol: item.symbol ?? "",
                  ),
        ),
      ],
      child: _EditConditionProfitStopLossOrder(
        item: item,
        onEditSuccess: onEditSuccess,
      ),
    ),
  ).copyWith(padding: EdgeInsets.zero).showSheet(context);
}

class _EditConditionProfitStopLossOrder extends StatefulWidget {
  const _EditConditionProfitStopLossOrder({
    required this.item,
    required this.onEditSuccess,
  });

  final ConditionOrderBookModel item;
  final VoidCallback onEditSuccess;

  @override
  State<_EditConditionProfitStopLossOrder> createState() =>
      _EditConditionProfitStopLossOrderState();
}

class _EditConditionProfitStopLossOrderState
    extends State<_EditConditionProfitStopLossOrder> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.item.symbol != null) {
        context.read<StockInfoCubit>().loadData(widget.item.symbol!);
      }
    });
  }

  Future<void> _fetchPortfolioAndUpdateCubit(
    BuildContext context,
    StockInfoModel stockInfo,
  ) async {
    try {
      final placeOrderCubit = context.read<PlaceOrderCubit>();
      final validateConditionOrderCubit =
          context.read<ValidateConditionOrderCubit>();

      // Fetch portfolio data using PlaceOrderCubit
      await placeOrderCubit.fetchDataSymbolHoldingPortfolio();

      if (!mounted) return;

      final placeOrderState = placeOrderCubit.state;
      final portfolioStockModel = placeOrderState.portfolioStockModel;

      // Update ValidateConditionOrderCubit with portfolio data
      validateConditionOrderCubit.updateParam(
        stockInfo: stockInfo,
        portfolioStockModel: portfolioStockModel,
        orderType:
            widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo
                ? OrderType.takeProfit
                : OrderType.stopLoss,
      );
    } catch (e) {
      // Handle error if needed
      debugPrint('Error fetching portfolio data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<AvailableTradeCubit, AvailableTradeState>(
          listenWhen:
              (previous, current) =>
                  previous.availableTrade != current.availableTrade,
          listener: (context, state) {
            context.read<ValidateOrderCubit>().updateParam(
              availableTrade: state.availableTrade,
            );
          },
        ),

        BlocListener<StockInfoCubit, StockInfoState>(
          listenWhen:
              (previous, current) =>
                  previous.stockInfo == null && current.stockInfo != null,
          listener: (context, state) {
            context.read<ValidateOrderCubit>().updateParam(
              stockInfo: state.stockInfo,
              action:
                  widget.item.orderTypeEnum == OrderTypeEnum.buy
                      ? OrderAction.buy
                      : OrderAction.sell,
              orderType:
                  widget.item.conditionOrderTypeEnum ==
                          ConditionOrderTypeEnum.tpo
                      ? OrderType.takeProfit
                      : OrderType.stopLoss,
            );

            // Fetch portfolio data and update ValidateConditionOrderCubit
            if (state.stockInfo != null) {
              _fetchPortfolioAndUpdateCubit(context, state.stockInfo!);
            }
          },
        ),

        BlocListener<PlaceOrderCubit, PlaceOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.portfolioStockModel != current.portfolioStockModel,
          listener: (context, state) {
            final stockInfoState = context.read<StockInfoCubit>().state;
            if (stockInfoState.stockInfo != null &&
                state.portfolioStockModel != null) {
              // Update ValidateConditionOrderCubit with portfolio data
              context.read<ValidateConditionOrderCubit>().updateParam(
                stockInfo: stockInfoState.stockInfo,
                portfolioStockModel: state.portfolioStockModel,
                orderType:
                    widget.item.conditionOrderTypeEnum ==
                            ConditionOrderTypeEnum.tpo
                        ? OrderType.takeProfit
                        : OrderType.stopLoss,
              );
            }
          },
        ),
      ],
      child: BlocBuilder<StockInfoCubit, StockInfoState>(
        builder: (context, state) {
          return GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.item.conditionOrderTypeEnum.titleEdit ?? '',
                          style: context.textStyle.body14?.copyWith(
                            color: vpColor.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        VPTextField(
                          hintText: widget.item.symbol ?? '',
                          textAlign: TextAlign.start,
                          inputType: InputType.disabled,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Edit Take Profit Stop Loss Order Widget
                  EditTakeProfitStopLossOrderWidget(
                    item: widget.item,
                    onEditSuccess: widget.onEditSuccess,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
